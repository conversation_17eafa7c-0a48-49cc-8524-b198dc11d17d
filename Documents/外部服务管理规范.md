---
tags:
  - t/resource
  - c/资料/文档
  - t/doc
上文: []
相关:
  - "[[项目组织]]"
  - "[[环境管理]]"
  - "[[依赖管理]]"
附件:
来源:
更新: ""
创建: 2025-07-22
---

# 外部服务管理规范

## 📋 概述

本规范定义了Ming-Digital-Garden项目中外部服务、第三方依赖和工具的标准化管理方式，确保项目结构清晰、依赖关系明确、环境管理统一。

## 🏗️ 三层架构管理体系

### 1. 系统级安装层 🍺

**管理工具**：Homebrew
**安装路径**：系统默认路径（`/opt/homebrew/` 或 `/usr/local/`）
**管理范围**：
- 系统级运行时环境（Python、Node.js、Git等）
- 核心开发工具（curl、wget、uv等）
- 系统服务和守护进程

**安装命令示例**：
```bash
brew install python@3.13
brew install node@20
brew install git
brew install uv
```

**Node.js全局工具**：
```bash
npm install -g @augmentcode/auggie  # Auggie CLI - AI代码助手
```

**管理原则**：
- ✅ 使用Homebrew统一管理所有系统级工具
- ✅ 定期更新：`brew update && brew upgrade && brew cleanup`
- ✅ 避免直接下载安装包，保持系统整洁

### 2. 环境级管理层 🐍

**管理位置**：`/Users/<USER>/Downloads/Ming-Digital-Garden/venv/`
**管理工具**：Python venv + pip
**管理范围**：
- Python依赖包和库
- MCP服务器的Python依赖
- 项目特定的Python工具

**环境创建**：
```bash
cd /Users/<USER>/Downloads/Ming-Digital-Garden
python3 -m venv venv
source venv/bin/activate
```

**依赖安装示例**：
```bash
# 激活环境
source venv/bin/activate

# 安装依赖
pip install 'markitdown[all]'
pip install requests beautifulsoup4
```

**管理原则**：
- ✅ 所有Python依赖统一安装在项目venv中
- ✅ 避免嵌套虚拟环境，保持环境简洁
- ✅ 定期清理不需要的依赖包

### 3. 源码级存储层 📦

**存储位置**：`/Users/<USER>/Downloads/Ming-Digital-Garden/Package/`
**管理范围**：
- 从GitHub克隆的第三方开源项目
- 必须使用但非自己开发的外部服务
- 需要本地部署的第三方工具和服务

**目录结构**：
```
Package/
├── MarkItDown/          # 文档转换工具
├── ProjectName1/        # 其他第三方项目
├── ProjectName2/        # 更多第三方项目
└── README.md           # 项目清单和说明
```

**管理原则**：
- ✅ 保持项目原名，与GitHub仓库名一致
- ✅ 完整克隆，包含LICENSE、README等文件
- ✅ 定期更新源码：`git pull origin main`

## 📁 目录命名规范

### Package目录选择理由

**为什么选择`Package`而不是`Vendors`？**

1. **图标独特性** ✅
   - Package在Material Icon for VSCode中有专门的文件夹图标
   - 与Library目录图标不同，视觉区分明确

2. **语义准确性** ✅
   - 准确描述"软件包/组件"的概念
   - 涵盖各种类型的第三方资源

3. **扩展性良好** ✅
   - 可容纳不同类型的第三方项目
   - 支持未来项目规模扩展

### 子目录命名规范

- **使用项目原名**：保持与GitHub仓库名一致
- **避免中文名称**：保持英文原名的专业性
- **可选数字前缀**：如需排序可使用（如`01-ProjectName`）

## 🖥️ 终端应用配置规范

### Shell环境标准化

**默认Shell**：zsh (macOS 10.15+ 系统默认)
**配置原则**：极简主义，最少文件数量，避免重复设置

#### 配置文件结构

```
~/.zprofile    # 登录shell配置，只包含brew shellenv
~/.zshrc       # 交互式shell配置，包含所有功能
```

**不使用的配置文件**：
- `~/.zshenv` - 已删除，避免文件分散
- `~/.bash_profile` - 保持最小化状态，仅作兼容
- `~/.bashrc` - 不使用

#### .zprofile 配置（极简）
```bash
eval "$(/opt/homebrew/bin/brew shellenv)"
```

#### .zshrc 配置（26行精简版）
```bash
# Aliases
alias python=python3
alias pip=pip3

# Prompt
eval "$(starship init zsh)"

# Git function
g() {
    cd /Users/<USER>/Downloads/Ming-Digital-Garden
    git add -A
    git commit -m "auto: sync changes at $(date)"
    git push
}

# Environment
export VIRTUAL_ENV_DISABLE_PROMPT=1
export GEMINI_API_KEY="AIzaSyBwMieat9Bpmpd53jqLFUBd1ZvtIfCUWFE"

# Auto-activate venv
auto_activate_venv() {
    [[ -f "venv/bin/activate" ]] && source venv/bin/activate > /dev/null 2>&1
}

chpwd() { auto_activate_venv; }
auto_activate_venv
```

#### Bash配置状态
- **当前状态**：最小化配置，仅作系统兼容
- **历史记录**：已清理，只保留必要标记
- **使用原则**：不主动使用bash，保持zsh为主shell

### 终端功能验证清单

**核心功能检查**：
- [ ] 虚拟环境自动激活（进入数字花园目录）
- [ ] API密钥正确设置（GEMINI_API_KEY）
- [ ] PATH无重复设置
- [ ] Git快捷函数正常工作
- [ ] Starship提示符正常显示

**配置验证命令**：
```bash
# 检查shell类型
echo $SHELL

# 检查虚拟环境激活
cd /Users/<USER>/Downloads/Ming-Digital-Garden && echo $VIRTUAL_ENV

# 检查API密钥
echo ${#GEMINI_API_KEY}

# 检查PATH重复
echo $PATH | tr ':' '\n' | sort | uniq -c | sort -nr
```

## 🔧 安装流程标准

### 新增外部服务标准流程

1. **需求评估**
   - 确认服务的必要性和用途
   - 评估维护成本和更新频率

2. **安装方式选择**
   ```mermaid
   flowchart TD
       A[新外部服务] --> B{服务类型}
       B -->|系统工具| C[Homebrew安装]
       B -->|Python服务| D[venv环境安装]
       B -->|源码项目| E[Package目录克隆]
       
       C --> F[系统级管理]
       D --> G[环境级管理]
       E --> H[源码级管理]
   ```

3. **安装执行**
   - 按照对应层级的管理原则执行安装
   - 记录安装过程和配置信息
   - 验证服务功能正常

4. **文档更新**
   - 更新本规范文档
   - 记录配置文件路径
   - 添加使用说明和注意事项

## 📊 管理检查清单

### 定期维护任务

**每月检查**：
- [ ] 系统级工具更新：`brew update && brew upgrade`
- [ ] Python依赖检查：`pip list --outdated`
- [ ] Package目录项目更新检查

**每季度检查**：
- [ ] 清理不需要的依赖：`brew cleanup`
- [ ] 虚拟环境健康检查
- [ ] Package目录项目版本更新

**年度检查**：
- [ ] 评估各服务的使用频率
- [ ] 清理不再需要的外部服务
- [ ] 更新管理规范和最佳实践

### 问题排查指南

**常见问题及解决方案**：

1. **工具调用失败**
   - 检查venv环境是否激活
   - 验证依赖是否正确安装
   - 检查PATH环境变量配置

2. **版本冲突**
   - 使用Homebrew管理系统级版本
   - 在venv中安装特定版本依赖
   - 避免全局Python包安装

3. **路径问题**
   - 使用绝对路径配置
   - 检查符号链接是否有效
   - 验证权限设置正确

## 🎯 最佳实践总结

### 核心原则

1. **分层管理**：系统、环境、源码三层分离
2. **统一标准**：使用标准化的安装和管理方式
3. **文档先行**：先更新规范，再执行操作
4. **定期维护**：建立定期检查和更新机制

### 成功指标

- ✅ 所有外部服务功能正常
- ✅ 依赖关系清晰明确
- ✅ 环境配置统一标准
- ✅ 项目结构整洁有序

---

## 📝 变更记录

| 日期 | 版本 | 变更内容 | 变更人 |
|------|------|----------|--------|
| 2025-07-22 | v1.0 | 初始版本创建，定义三层架构管理体系 | Pepper |
| 2025-07-24 | v1.1 | 新增终端应用配置规范，定义zsh极简配置标准 | Pepper |
| 2025-07-30 | v1.2 | 执行Gemini CLI更新（0.1.13→0.1.15），验证Homebrew管理流程 | Pepper |
| 2025-08-04 | v1.3 | 新增Auggie CLI (v0.1.0)，通过npm全局安装管理 | Pepper |
| 2025-08-04 | v1.4 | 执行Gemini CLI更新（0.1.15→0.1.16），验证npm强制更新流程 | Pepper |

---

*本规范将根据项目发展和实际使用情况持续更新优化*
