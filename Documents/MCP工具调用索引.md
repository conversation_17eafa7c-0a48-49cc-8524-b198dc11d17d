---
tags:
  - t/resource
  - c/资料/索引
  - t/doc
  - m/tips
  - m/list
  - c/资料/文档
上文: []
相关:
  - "[[MCP]]"
  - "[[工具]]"
  - "[[API]]"
  - "[[Vision]]"
附件:
来源:
更新: ""
描述: AI工具快速查找索引，按功能分类的完整MCP工具列表，包含所有可用的外部工具和内置工具，由Vision角色维护更新
标题: MCP工具调用索引
创建: 2025-08-04
---

# MCP工具调用索引

## 🔍 工具索引

### 🧠 思维分析
- `sequentialthinking_Sequential_thinking` - 复杂问题分步分析

### 🎯 PromptX专业能力增强
- `promptx_init_promptx` - 初始化PromptX工作环境，创建配置目录和项目资源注册表
- `promptx_welcome_promptx` - 展示所有可用的AI专业角色和工具清单
- `promptx_action_promptx` - 激活特定AI专业角色，获得专业思维和技能包
- `promptx_learn_promptx` - 学习专业资源，通过标准化协议体系加载各类专业资源
- `promptx_remember_promptx` - 基于认知心理学模拟人类记忆编码-存储-检索机制
- `promptx_recall_promptx` - 基于检索心理学的记忆激活和提取机制
- `promptx_tool_promptx` - 执行通过@tool协议声明的JavaScript功能工具

### 🌐 浏览器操作 (Playwright)
- `browser_navigate_Playwright` - 导航到指定URL
- `browser_click_Playwright` - 在网页上执行点击操作
- `browser_type_Playwright` - 向可编辑元素输入文本
- `browser_take_screenshot_Playwright` - 截取当前页面的屏幕截图
- `browser_wait_for_Playwright` - 等待文本出现或消失或指定时间过去
- `browser_snapshot_Playwright` - 捕获当前页面的可访问性快照，比截图更好
- `browser_close_Playwright` - 关闭页面
- `browser_resize_Playwright` - 调整浏览器窗口大小
- `browser_hover_Playwright` - 悬停在页面元素上
- `browser_select_option_Playwright` - 在下拉菜单中选择选项
- `browser_handle_dialog_Playwright` - 处理对话框
- `browser_file_upload_Playwright` - 上传一个或多个文件
- `browser_press_key_Playwright` - 在键盘上按键
- `browser_navigate_back_Playwright` - 返回上一页
- `browser_navigate_forward_Playwright` - 前进到下一页
- `browser_tab_new_Playwright` - 打开新标签页
- `browser_tab_close_Playwright` - 关闭标签页
- `browser_tab_select_Playwright` - 通过索引选择标签页
- `browser_tab_list_Playwright` - 列出浏览器标签页
- `browser_drag_Playwright` - 在两个元素之间执行拖放
- `browser_console_messages_Playwright` - 返回所有控制台消息
- `browser_network_requests_Playwright` - 返回加载页面以来的所有网络请求
- `browser_evaluate_Playwright` - 在页面或元素上评估JavaScript表达式
- `browser_install_Playwright` - 安装配置中指定的浏览器

### 🔍 网络搜索和检索 (Exa AI)
- `web_search_exa_Exa_Search` - 使用Exa AI进行实时网络搜索，可配置结果数量
- `company_research_exa_Exa_Search` - 研究公司和企业信息，提供商业洞察和分析
- `crawling_exa_Exa_Search` - 从特定URL提取和爬取内容，支持元数据和结构化信息
- `linkedin_search_exa_Exa_Search` - 搜索LinkedIn档案和公司页面，用于专业网络研究
- `deep_researcher_start_Exa_Search` - 启动AI驱动的深度研究任务，进行复杂查询分析
- `deep_researcher_check_Exa_Search` - 检查深度研究任务状态并获取结果

### 🔍 通用网络搜索
- `brave_web_search_Brave_Search` - 使用Brave搜索API进行通用查询，支持分页和过滤
- `brave_local_search_Brave_Search` - 搜索本地商家和地点，返回详细商业信息
- `web-search` - 搜索网络信息，使用Google自定义搜索API
- `web-fetch` - 从网页获取数据并转换为Markdown
- `tavily_search_tavily-remote-mcp` - 实时网络信息搜索，支持多种搜索主题和过滤
- `tavily_extract_tavily-remote-mcp` - 从特定网页提取和处理内容，返回结构化格式
- `tavily_crawl_tavily-remote-mcp` - 从网站爬取多个页面，支持外部链接和分类过滤
- `tavily_map_tavily-remote-mcp` - 发现网站结构，映射所有URL和页面关系

### 🕷️ 网页抓取 (Firecrawl)
- `firecrawl_scrape_firecrawl-mcp` - 从单个URL提取内容，支持高级选项和缓存，最强大的单页抓取工具
- `firecrawl_map_firecrawl-mcp` - 发现网站所有索引URL，了解网站结构和页面关系
- `firecrawl_crawl_firecrawl-mcp` - 异步爬取网站多个页面并提取内容，支持深度爬取
- `firecrawl_check_crawl_status_firecrawl-mcp` - 检查爬取任务状态和进度，监控异步任务
- `firecrawl_search_firecrawl-mcp` - 网络搜索并可选择性提取搜索结果内容，最强大的搜索工具
- `firecrawl_extract_firecrawl-mcp` - 使用LLM从网页提取结构化信息，支持自定义Schema
- `firecrawl_deep_research_firecrawl-mcp` - 进行深度网络研究，智能爬取、搜索和LLM分析
- `firecrawl_generate_llmstxt_firecrawl-mcp` - 为域名生成标准化llms.txt文件，定义AI交互规则

### 🐙 GitHub集成
- `search_repositories_github` - 搜索GitHub仓库
- `create_repository_github` - 在账户中创建新的GitHub仓库
- `get_file_contents_github` - 获取GitHub仓库中文件或目录的内容
- `create_or_update_file_github` - 在GitHub仓库中创建或更新单个文件
- `push_files_github` - 单次提交推送多个文件到GitHub仓库
- `create_issue_github` - 在GitHub仓库中创建新issue
- `list_issues_github` - 列出GitHub仓库中的issues，支持过滤选项
- `get_issue_github` - 获取GitHub仓库中特定issue的详情
- `update_issue_github` - 更新GitHub仓库中的现有issue
- `add_issue_comment_github` - 为现有issue添加评论
- `create_pull_request_github` - 在GitHub仓库中创建新pull request
- `list_pull_requests_github` - 列出并过滤仓库pull requests
- `get_pull_request_github` - 获取特定pull request的详情
- `merge_pull_request_github` - 合并pull request
- `create_pull_request_review_github` - 为pull request创建审查
- `fork_repository_github` - Fork GitHub仓库到账户或指定组织
- `create_branch_github` - 在GitHub仓库中创建新分支
- `list_commits_github` - 获取GitHub仓库分支的提交列表
- `search_code_github` - 在GitHub仓库中搜索代码
- `search_issues_github` - 在GitHub仓库中搜索issues和pull requests
- `search_users_github` - 在GitHub上搜索用户
- `get_pull_request_files_github` - 获取pull request中更改的文件列表
- `get_pull_request_status_github` - 获取pull request所有状态检查的综合状态
- `get_pull_request_comments_github` - 获取pull request的审查评论
- `get_pull_request_reviews_github` - 获取pull request的审查
- `update_pull_request_branch_github` - 用基础分支的最新更改更新pull request分支

### 📚 文档查询和转换
- `resolve-library-id_Context_7` - 解析包/产品名称为Context7兼容的库ID，支持模糊匹配
- `get-library-docs_Context_7` - 获取库的最新文档，需要Context7兼容的库ID，支持主题聚焦
- `deepwiki_fetch_mcp-deepwiki` - 获取deepwiki.com仓库并返回Markdown格式，支持深度抓取
- `convert_to_markdown_markitdown-mcp` - 将HTTP、HTTPS、文件和数据URI转换为Markdown格式

### ⏰ 时间工具
- `get_current_time_mcp-server-time` - 获取特定时区的当前时间，支持IANA时区名称
- `convert_time_mcp-server-time` - 在不同时区之间转换时间，支持24小时格式

### 📄 API文档
- `read_project_oas_upuxhv_TikHub_io_API_Docs` - 读取TikHub.io社交媒体数据API文档内容
- `read_project_oas_ref_resources_upuxhv_TikHub_io_API_Docs` - 读取TikHub API引用资源和组件定义
- `refresh_project_oas_upuxhv_TikHub_io_API_Docs` - 从服务器重新下载最新TikHub API文档
- `read_project_oas_1cx29z____API_-_API___` - 读取飞书API文档内容和接口定义
- `read_project_oas_ref_resources_1cx29z____API_-_API___` - 读取飞书API引用资源和数据模型
- `refresh_project_oas_1cx29z____API_-_API___` - 从服务器重新下载最新飞书API文档

### 🖥️ 系统文件操作 (Desktop Commander)
- `get_config_Desktop_Commander` - 获取完整的服务器配置为JSON格式
- `set_config_value_Desktop_Commander` - 通过键设置特定配置值
- `read_file_Desktop_Commander` - 从文件系统或URL读取文件内容，支持偏移和长度参数
- `read_multiple_files_Desktop_Commander` - 同时读取多个文件的内容
- `write_file_Desktop_Commander` - 写入或追加文件内容，标准做法是分块写入
- `create_directory_Desktop_Commander` - 创建新目录或确保目录存在
- `list_directory_Desktop_Commander` - 获取指定路径中所有文件和目录的详细列表
- `move_file_Desktop_Commander` - 移动或重命名文件和目录
- `search_files_Desktop_Commander` - 通过名称使用大小写不敏感的子字符串匹配查找文件
- `search_code_Desktop_Commander` - 使用ripgrep在文件内容中搜索文本/代码模式
- `get_file_info_Desktop_Commander` - 获取文件或目录的详细元数据
- `edit_block_Desktop_Commander` - 对文件应用精确的文本替换

### 🔧 系统进程管理 (Desktop Commander)
- `start_process_Desktop_Commander` - 启动新的终端进程，支持智能状态检测
- `read_process_output_Desktop_Commander` - 从运行进程读取输出，支持智能完成检测
- `interact_with_process_Desktop_Commander` - 向运行进程发送输入并自动接收响应
- `force_terminate_Desktop_Commander` - 强制终止运行的终端会话
- `list_sessions_Desktop_Commander` - 列出所有活动的终端会话
- `list_processes_Desktop_Commander` - 列出所有运行的进程
- `kill_process_Desktop_Commander` - 通过PID终止进程
- `get_usage_stats_Desktop_Commander` - 获取调试和分析的使用统计
- `give_feedback_to_desktop_commander_Desktop_Commander` - 在浏览器中打开反馈表单

### 📄 代码库和文件操作 (内置核心)
- `codebase-retrieval` - Augment的上下文引擎，搜索和检索代码库中的相关代码片段
- `git-commit-retrieval` - 基于Git提交历史搜索相关信息，了解代码变更历史
- `view` - 查看文件和目录内容，支持正则表达式搜索和范围查看
- `str-replace-editor` - 编辑现有文件，支持字符串替换和插入操作
- `save-file` - 创建新文件，限制内容最多300行
- `remove-files` - 删除文件，用户工作区中删除文件的唯一安全工具

### 🔧 进程和终端操作 (内置核心)
- `launch-process` - 启动shell命令和进程，支持等待和后台模式
- `read-process` - 读取进程输出，支持智能完成检测
- `write-process` - 向进程写入输入，用于交互式操作
- `kill-process` - 通过终端ID终止进程
- `list-processes` - 列出所有活动进程及其状态
- `read-terminal` - 读取VSCode终端输出，支持选中文本读取

### � GitHub集成 (内置核心)
- `github-api` - 调用GitHub API，支持仓库、问题、PR、提交等操作

### �📋 任务管理工具
#### shrimp-task-manager (专业任务管理 - 推荐)
- `plan_task_shrimp-task-manager` - 任务规划指导器，禁止假设猜测，必须收集信息
- `analyze_task_shrimp-task-manager` - 深入分析任务需求，评估技术可行性，使用pseudocode格式
- `reflect_task_shrimp-task-manager` - 批判性审查分析结果，识别优化机会
- `split_tasks_shrimp-task-manager` - 复杂任务分解，建立依赖关系（1-2工作天粒度，最多10项子任务）
- `list_tasks_shrimp-task-manager` - 生成结构化任务清单，包含状态追踪和优先级
- `execute_task_shrimp-task-manager` - 获取任务执行指导（注意：工具提供指导，需按步骤执行）
- `verify_task_shrimp-task-manager` - 任务验证评分，80分以上自动完成，低于80分提供修正建议
- `update_task_shrimp-task-manager` - 更新任务内容，已完成任务仅可更新摘要和相关文件
- `delete_task_shrimp-task-manager` - 删除未完成任务，保护已完成任务完整性
- `clear_all_tasks_shrimp-task-manager` - 清除未完成任务，重置任务列表
- `query_task_shrimp-task-manager` - 根据关键字或ID搜索任务
- `get_task_detail_shrimp-task-manager` - 获取任务完整详细信息
- `process_thought_shrimp-task-manager` - 灵活思考流程，建立质疑验证修正想法
- `init_project_rules_shrimp-task-manager` - 初始化或更新项目规范文件
- `research_mode_shrimp-task-manager` - 程式编程深度研究模式

#### 内置任务管理 (基础功能)
- `view_tasklist` - 查看当前任务列表
- `add_tasks` - 添加新任务或子任务
- `update_tasks` - 更新任务状态、名称和描述
- `reorganize_tasklist` - 重组任务列表结构

### 🔍 其他核心功能 (内置)
- `diagnostics` - 获取IDE问题诊断（错误、警告等）
- `open-browser` - 在默认浏览器中打开URL
- `view-range-untruncated` - 查看截断内容的特定范围
- `search-untruncated` - 在截断内容中搜索术语
- `render-mermaid` - 渲染Mermaid图表，支持交互式图表展示

### 📣 交互控制
- `zhi___` - 智能代码审查交互工具，支持预定义选项、自由文本输入和图片上传

### ⛔ 禁用工具说明
以下工具在特定角色或场景下被禁用，不应使用：

#### 全局禁用工具
- ~~`add_tasks`~~ - 禁用：必须使用shrimp-task-manager专业任务管理
- ~~`update_tasks`~~ - 禁用：必须使用shrimp-task-manager专业任务管理
- ~~`view_tasklist`~~ - 禁用：必须使用shrimp-task-manager专业任务管理
- ~~`reorganize_tasklist`~~ - 禁用：必须使用shrimp-task-manager专业任务管理
- ~~`remember`~~ - 禁用：必须使用promptx_remember_promptx认知记忆系统
- ~~`promptx_think_promptx`~~ - 禁用：必须使用process_thought_shrimp-task-manager思考流程

#### 禁用原因
- **任务管理工具禁用**：所有角色都要求使用专业级shrimp-task-manager，确保任务管理的专业性和一致性
- **记忆工具禁用**：统一使用PromptX认知记忆系统，提供更强大的语义网络和记忆机制
- **思考工具禁用**：统一使用shrimp-task-manager的process_thought，提供更灵活的思考流程
- **违规零容忍**：发现使用禁用工具将立即停止并纠正为正确工具

---

## 📊 工具统计

### 按类别统计
- **思维分析**: 1个工具
- **PromptX专业能力增强**: 7个工具
- **浏览器操作**: 24个工具
- **网络搜索和检索 (Exa AI)**: 6个工具
- **通用网络搜索**: 8个工具
- **网页抓取**: 8个工具
- **GitHub集成**: 26个工具
- **文档查询和转换**: 4个工具
- **时间工具**: 2个工具
- **API文档**: 6个工具
- **系统文件操作**: 11个工具
- **系统进程管理**: 9个工具
- **代码库和文件操作**: 6个工具
- **进程和终端操作**: 6个工具
- **GitHub集成 (内置核心)**: 1个工具
- **任务管理**: 19个工具
- **其他核心功能**: 5个工具
- **交互控制**: 1个工具
- **禁用工具**: 6个工具

**总计**: 156个工具（150个可用 + 6个禁用）

### 工具使用建议
- **文档整理推荐**: 使用shrimp-task-manager进行复杂项目管理
- **网络搜索推荐**: firecrawl_search为最强大的搜索工具
- **文件操作推荐**: Desktop Commander系列工具功能最全面
- **交互规范**: 必须使用zhi___工具进行用户交互，遵循寸止协议

---
*最后更新: 2025-08-01 by Vision角色*
