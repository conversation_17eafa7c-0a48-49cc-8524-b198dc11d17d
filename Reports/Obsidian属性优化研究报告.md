---
tags:
  - c/资料/研究报告
  - t/resource
  - t/research
上文: []
相关: []
附件:
来源:
更新:
创建: 2025-08-03
---
# Obsidian 通用属性模板优化研究报告

**研究时间**: 2025-07-17  
**研究目标**: 为Ming-Digital-Garden设计最优的通用YAML属性模板  
**适用场景**: 笔记、灵感、想法、网络摘录、Readwise同步、Web Clipper数据

---

## 📊 现有模板分析

### 当前T-Meta.md模板问题
- **字段过多**: 59个字段，复杂度过高
- **冗余严重**: name/title、summary/description等重复字段
- **分类混乱**: 工作流字段分散，缺乏逻辑层次
- **适用性差**: 技术字段对笔记类内容不适用
- **不符合最佳实践**: 日期格式不标准，缺少核心字段

### 社区最佳实践要点¹
- **一致性优先**: 标准化字段名和格式
- **简单性原则**: 避免过度复杂化
- **Dataview兼容**: 优化查询性能
- **模块化设计**: 分层次的字段结构

---

## 🎯 优化设计方案（基于实际使用情况修正）

### 实际文件分析发现

**Readwise实际字段**：`title`, `author`, `tags`, `topics`, `url`
**Web Clipper实际字段**：`title`, `source`, `author`, `published`, `created`, `description`, `tags`

### 去除"幻想"字段

经用户指正，以下字段实际不会使用：
- `status`, `priority`, `rating` - 不会手动管理
- `project`, `related` - 不会在属性中关联
- `readwise_id`, `clip_date` - 系统不提供或冗余
- `keywords`, `notes`, `next_action` - 实际不需要
- `summary` - 实际使用`description`
- `topic` - 实际使用`topics`（复数）

### 基于Video Factory项目文档的再次优化

**Video Factory文档实际使用字段分析**：
- 项目文档使用了约15个字段（vs 普通内容的6-8个）
- 但仍有75%的字段空置
- 不同内容类型确实有不同的字段需求

### 最终分层字段架构（18个字段）

```yaml
---
# === 第一层：核心字段（所有内容都可能用到）===
title:
created: {{date:YYYY-MM-DD}}
tags: []
author: []
source:
url:
description:
type: # note/idea/inspiration/clip/readwise/web-clip/project

# === 第二层：组织字段（项目文档和复杂内容用到）===
status: # draft/active/archived/completed
version:
project: []
topic: []
relate: []
next:

# === 第三层：扩展字段（特殊场景用到）===
rating: # 1-5
aliases: []
updated:
summary:
language:
published:
---
```

### 最终优化效果
- **字段数量**: 从59个减少到18个（减少69%）
- **分层设计**: 按使用频率分为三层，提供清晰的使用指导
- **全场景覆盖**: 既满足简单内容需求，也支持复杂项目文档
- **实用平衡**: 在简洁性和功能性之间找到最佳平衡点

---

## 📋 各内容类型适配示例

### 笔记（Note）
```yaml
type: note
title: "Obsidian属性最佳实践"
created: 2025-07-17
tags: [obsidian, metadata, best-practice]
status: active
summary: "研究Obsidian YAML属性的最佳实践"
project: "数字花园优化"
topic: "知识管理"
```

### 灵感（Inspiration）
```yaml
type: inspiration
title: "AI辅助笔记整理的想法"
created: 2025-07-17
tags: [ai, automation, idea]
status: draft
summary: "利用AI自动整理和分类笔记"
priority: high
next_action: "研究相关AI工具"
```

### 网络摘录（Clip）
```yaml
type: clip
title: "Obsidian YAML最佳实践"
created: 2025-07-17
tags: [obsidian, yaml, tutorial]
status: active
source: "YouTube"
author: "Nicole van der Hoeven"
url: "https://www.youtube.com/watch?v=RrxqkIhh9L8"
rating: 5
clip_date: 2025-07-17
```

### Readwise同步
```yaml
type: readwise
title: "Building a Second Brain - Chapter 3"
created: 2025-07-17
tags: [book, pkm, highlights]
status: active
source: "Building a Second Brain"
author: "Tiago Forte"
readwise_id: "12345"
rating: 4
```

---

## 🚀 实施建议

### 1. 渐进式迁移策略
- 新笔记立即使用新模板
- 现有笔记按需逐步迁移
- 保留原模板作为备份

### 2. 字段映射规则
```
现有字段 → 新字段
name → title
description → summary
up/down/relate → related
mark → notes
```

### 3. 类型标准化
建立严格的type值规范：
- `note`: 常规笔记
- `idea`: 想法记录
- `inspiration`: 灵感捕获
- `clip`: 网络摘录
- `readwise`: Readwise同步
- `web-clip`: Web Clipper数据

### 4. Dataview查询优化
新模板支持高效查询：
```dataview
TABLE summary, author, rating
FROM ""
WHERE type = "clip" AND rating >= 4
SORT created DESC
```

---

## 📚 参考资料

1. Nicole Van der Hoeven's Obsidian Playbook - YAML Front Matter²
2. Obsidian Community Forum - YAML Best Practices³
3. The Sweet Setup - Obsidian Metadata with YAML and Dataview⁴
4. Reddit r/ObsidianMD - Metadata Discussions⁵

---

**脚注**:
¹ 基于对Obsidian社区、YouTube教程、Reddit讨论的深度研究
² https://notes.nicolevanderhoeven.com/obsidian-playbook/Using+Obsidian/03+Linking+and+organizing/YAML+Frontmatter
³ https://forum.obsidian.md/t/how-do-you-put-yaml-to-use-in-your-system/18987
⁴ https://thesweetsetup.com/obsidian-metadata-with-yaml-and-dataview/
⁵ https://www.reddit.com/r/ObsidianMD/comments/138cqan/metadata_in_obsidian/
