---
人员: 
  - "[[<PERSON>]]"
tags:
  - c/资料/articles
日期: 2025-08-01
时间: None
相关:
链接: https://mp.weixin.qq.com/s/YLxrvN_N1juSKiHXFqKaSA
附件: https://mmbiz.qpic.cn/mmbiz_jpg/yE2L51Z87TtbzOxDib1mATn5Q8j7gDxZQxvFvQVXeO0BlDbSIcKgfa3re852OHOaBG4u5ltAENrcGsKT53ykFCw/0?wx_fmt=jpeg)
---
## Document Note

## Summary

Claude Code别再使用SPEC了，把需求拆成\x26quot;专业废纸\x26quot;，实现全是屎，还不如随手撸核心要点（30秒看完

## Full Document
### Claude Code别再使用SPEC了，把需求拆成"专业废纸"，实现全是屎，还不如随手撸

#### 核心要点（30秒看完）

**问题**：Spec 通过一句话生成 requirements → design → tasks，看起来专业完整，实际执行结果是垃圾。

**原因**：一句话输入 → 自动生成一堆"专业文档" → 但缺少最关键的**需求确认**环节 → 生成的都是错误假设。

**解决方案**：requirements-pilot —— 不急着生成文档，先通过对话确认需求（≥90分），再生成可执行规格。

**行动**：抛弃 Spec，Claude Code 安装 requirements-pilot，从确认需求开始。

#### 一、Spec 的美丽谎言

你肯定见过这样的演示：

1. 输入："为邀请码增加有效期功能"
2. Spec 自动生成：

* ✅ Requirements 文档（10页）
* ✅ Design 文档（15页）
* ✅ Tasks 列表（30个）

1. 看起来好专业！好完整！

**然后呢？执行时全崩了。**

#### 二、为什么Spec 生成的是垃圾？

##### 问题1：一句话藏着100个坑

"为邀请码增加有效期功能" —— Kiro Spec 会自作主张脑补：

* 假设有效期从创建时算（实际可能要从激活时算）
* 假设用 UTC 时区（实际系统用本地时区）
* 假设过期返回 401（实际应该返回自定义错误码）
* 假设老数据不管（实际需要兼容处理）

**这些错误假设被包装成"专业文档"，执行时才发现全错了。**

##### 问题2：过度细化，撕裂上下文

文档把接口、数据结构、异常、时序拆成孤岛。纸面整齐，代码成渣。到实现阶段，你得把被拆碎的东西一针一线缝回去：接口卡口半毫米对不上，边界条件到处漏，越补越碎，越缝越厚。

**结果**：开发者要花更多时间理解这些"垃圾文档"，还不如直接写代码。

##### 问题3：串行慢反馈，返工巨大

流程是"规格→实现→验证"，结论最晚才出来。一个中等功能，走完整条流水线才发现某个设计假设不成立——这时候改动像推倒重来，代价翻倍。

##### 问题4：质量肉眼下降，不如 vibe

真事：按 SPEC 做，集成和测试阶段崩得更狠；反倒是"随手撸"的原型对齐现状、问题更少。原因明白得很：SPEC 的"默认假设"跟仓库的真实约束错位。为了服务文档，不得不堆适配层，复杂度直线上天。

#### 三、"一句话生成"的两个致命误导

* **伪确定性**：一句话盖住关键歧义——时区怎么算、起算口径从哪来、失败怎么回退、如何兼容存量、灰度怎么切。生成器会选一个默认，但八成与系统既有约定冲突。你看不到坑，它就在上线前一起炸。
* **不可验证**：没有"通过/失败"的可证伪标准。测试只能临时拼。一旦爆雷，已是最贵时刻。

#### 四、spec = 一句话的精装修

把一句话拆成看起来专业的章节、画上好看的表格，但依旧不告诉你"具体怎么改代码"。于是实施全靠猜，补丁贴满身，交付物像缝合怪，和现有系统就是不贴。

#### 五、替代路径：先确认，再实现（requirements-pilot）

别把文档写得更厚，先把歧义打光。动作不多，但每一步都往"可执行"走。

##### 核心区别

|  | Spec | requirements-pilot |
| --- | --- | --- |
| **输入** | 一句话 → 自动脑补 | 一句话 → 开始对话 |
| **过程** | 生成一堆文档 | 通过问答确认细节 |
| **输出** | 看着专业的废纸 | 可直接执行的规格 |
| **结果** | 执行时发现全错 | 执行时按规格跑通 |

##### 工作原理

**不是让 AI 猜，而是让它问：**

```
你："为邀请码增加有效期功能"  
  
AI："有效期从什么时候开始计算？  
    A) 邀请码创建时  
    B) 用户首次使用时  
    C) 用户激活账号时"  
  
你："B，首次使用时"  
  
AI："过期的邀请码应该返回什么错误？  
    A) HTTP 401 Unauthorized    
    B) HTTP 403 Forbidden  
    C) 自定义错误码（请指定）"  
  
你："C，错误码 INVITATION_EXPIRED = 4001"  

```

**每个回答都被记录，最终生成的规格是基于确认的事实，不是猜测。**

##### 两道门禁

* **门禁一：需求确认≥90分**

四个维度必须过线：

产物：`requirements-confirm.md`（原始请求、往返澄清、评分与最终口径）。
+ 功能讲清输入/输出/成功判据
+ 技术具体到接口/字段/状态码/时序
+ 实现覆盖边界与失败路径
+ 业务说清"为什么要这么做"

* **门禁二：需求方明确批准**

由需求方说"按此实现"。理解对但不是他要的？不准开工。

##### Auto Pilot Sub-Agents 链路

AI 总结需求评分并询问是否开始，用户确认后 auto pilot sub-agents 自动执行：

* **requirements-generate**：自动合成单文档规格 `requirements-spec.md`，只写可落地动作：文件路径、函数签名、请求/响应示例、错误码、迁移/回滚脚本、配置与灰度、监控指标。
* **requirements-code**：自动按规格直改代码，少抽象，先跑通。
* **requirements-review**：自动按功能/集成/质量/性能四象限打分；<90退回重来；≥90进测试。
* **requirements-testing**：自动围绕规格的关键路径、边界与失败场景写"真能用"的测试。

**Auto Pilot 全程无需人工干预**，用户只需在 AI 询问时确认开始执行。

#### 六、实战对比：同样的需求，不同的结果

##### Kiro Spec 的输出（看着很美）

```
## Requirements  
- The system shall implement invitation code expiration  
- Codes shall have configurable validity periods  
- Expired codes shall be handled gracefully  
  
## Design    
- Add expiration_date field to invitation table  
- Implement ExpiredInvitationException  
- Create InvitationValidator service  
  
## Tasks  
1. Update database schema  
2. Implement validation logic  
3. Add API endpoints  
4. Write unit tests  
...（还有26个）  

```

**问题**：全是正确的废话，没有可执行细节。

##### requirements-pilot 的输出（直接能用）

```
## 已确认规格  
  
数据库变更：  
- ALTER TABLE invitations ADD expires_at TIMESTAMP NULL;  
- UPDATE invitations SET expires_at = created_at + INTERVAL '7 days' WHERE expires_at IS NULL;  
  
验证函数：  
- validateInvitation(code: string): ValidationResult  
- 首次使用时设置 first_used_at，计算 expires_at = first_used_at + validity_days  
- 过期返回：{ valid: false, error: 'INVITATION_EXPIRED', code: 4001 }  
  
API 变更：  
- POST /invitations/validate 增加错误码 4001  
- 响应示例：{ "error": "INVITATION_EXPIRED", "message": "邀请码已过期" }  

```

**区别**：每一行都是可以直接复制执行的命令。

#### 七、在 Claude Code 中使用 requirements-pilot

##### Claude Code 的革命性优势

requirements-pilot 不是普通工具，而是 **Claude Code 的原生 slash 命令**，这意味着：

* **无缝集成**：直接在 Claude Code 中使用，无需切换工具
* **上下文保持**：与你的代码仓库完全联通
* **专家协作**：可以与其他 Claude Code sub-agents 组合使用

##### 1. 安装 requirements-pilot

```
git clone https://github.com/cexll/myclaude  
mkdir -p ~/.claude/{commands,agents}  
cp -R myclaude/commands/* ~/.claude/commands/  
cp -R myclaude/agents/* ~/.claude/agents/  

```

##### 2. 在 Claude Code 中的使用体验

```
# 传统 spec-workflow（问题多）  
/spec-workflow "为邀请码增加有效期"    
# → 自动生成 requirements → design → tasks → code  
# → 但基于错误假设，执行结果是垃圾  
  
# requirements-pilot（推荐）  
/requirements-pilot "为邀请码增加有效期"  
# → AI 总结需求评分并询问是否开始 → 用户确认 → auto pilot 执行：generate → code → review → testing  

```

##### 3. 两种工作流对比

**传统 spec-workflow 链条**：

```
一句话 → 自动生成规格 → 自动实现 → 自动测试  
问题：全程都是基于错误假设，看起来自动化，实际是自动制造垃圾  

```

**requirements-pilot 智能链条**：

```
一句话 → AI 总结需求评分并询问 → 用户确认开始 → auto pilot 执行：generate → code → review → testing  
优势：基于确认事实的 auto pilot，既快速又可控  

```

##### 4. requirements-pilot 的 Auto Pilot 优势

相比需要手动触发每个步骤的传统方法，requirements-pilot 实现了：

* **智能评分确认**：AI 自动总结需求、评分并询问用户是否开始执行
* **Auto Pilot 链式执行**：用户确认后通过 auto pilot sub-agents 自动完成整个流程
* **质量门控**：review 阶段评分<90自动返回重做，≥90才进入测试
* **一键式体验**：只需回复确认，整个实现过程完全自动化

```
# 简单确认即可完成全流程  
/requirements-pilot "为邀请码增加有效期功能"  
# AI: "需求已理解，评分92分，是否开始执行？"  
# 用户: "确认"  
# → auto pilot 自动执行：generate → code → review → testing  

```

##### 5. 关键差异总结

|  | Spec 自动链 | requirements-pilot 智能链 |
| --- | --- | --- |
| **需求确认** | ❌ 自动脑补 | ✅ 对话确认 |
| **执行方式** | 全自动（基于假设） | 分段式（确认+Auto Pilot） |
| **错误处理** | 最后才发现全错 | 确认阶段避免错误，review阶段质量门控 |
| **结果质量** | 1分钟垃圾 | AI 评分确认 + Auto Pilot 精品实现 |
| **用户参与** | 无参与，盲目执行 | AI 询问时确认开始，Auto Pilot 执行 |
| **Claude Code 集成** | ✅ 原生支持 | ✅ 原生支持 |

#### 八、记住这个教训

一句话生成的"专业文档"都是包装精美的垃圾。真正的需求理解来自于对话和确认。

**别被 Spec 的表面功夫骗了。需求不确认清楚，生成再多文档也是浪费时间。**

别把工具当银弹，把确认当形式。真正的效率来自"先把话说清楚，再让工具把重复劳动做干净"。
